import { storage } from "./storage";
import type { InsertItem, InsertUser } from "@shared/schema";

export async function seedDatabase() {
  try {
    // Create sample users
    const sampleUsers: InsertUser[] = [
      {
        id: "admin-user-1",
        email: "<EMAIL>",
        firstName: "Admin",
        lastName: "User",
        role: "admin",
        isGuest: false,
      },
      {
        id: "security-user-1", 
        email: "<EMAIL>",
        firstName: "Security",
        lastName: "Officer",
        role: "security",
        isGuest: false,
      }
    ];

    for (const user of sampleUsers) {
      try {
        await storage.upsertUser(user);
      } catch (error) {
        console.log(`User ${user.email} might already exist`);
      }
    }

    // Create sample items with the new schema
    const sampleItems: InsertItem[] = [
      {
        title: "iPhone 14 Pro",
        description: "Black iPhone 14 Pro found near the library main entrance. Has a blue protective case with initials '<PERSON><PERSON>' written on it. Phone is locked but shows missed calls.",
        category: "electronics",
        location: "library",
        specificLocation: "Main entrance, near the information desk",
        dateLostFound: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        type: "found",
        reporterName: "<PERSON>",
        reporterEmail: "<EMAIL>",
        reporterPhone: "******-0123",
        images: ["https://images.unsplash.com/photo-1592750475338-74b7b21085ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"],
        mapLocation: { lat: 40.7589, lng: -73.9851 }
      },
      {
        title: "Blue JanSport Backpack",
        description: "Navy blue JanSport backpack found in Cafeteria B near table 15. Contains textbooks for Biology 101 and Chemistry lab notebook with name 'Alex Chen'.",
        category: "accessories",
        location: "cafeteria",
        specificLocation: "Cafeteria B, near the south entrance",
        dateLostFound: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        type: "found",
        reporterName: "Mike Davidson",
        reporterEmail: "<EMAIL>",
        reporterPhone: "******-0124",
        images: ["https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"],
        mapLocation: { lat: 40.7580, lng: -73.9840 }
      },
      {
        title: "Set of Keys with Owl Keychain",
        description: "Set of 4 keys with a small brown owl keychain. Found on the ground near the gymnasium entrance. Includes what appears to be dorm room and locker keys.",
        category: "keys",
        location: "gym",
        specificLocation: "Outside gymnasium, near bike racks",
        dateLostFound: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        type: "found",
        reporterName: "Alex Kim",
        reporterEmail: "<EMAIL>",
        reporterPhone: "******-0125",
        images: ["https://images.unsplash.com/photo-1582139329536-e7284fece509?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"],
        mapLocation: { lat: 40.7595, lng: -73.9845 }
      },
      {
        title: "MacBook Air Laptop",
        description: "LOST: 13-inch MacBook Air with silver finish. Has stickers from various tech companies on the lid. Last seen in the computer lab in Engineering Building.",
        category: "electronics",
        location: "engineering",
        specificLocation: "Computer Lab, 3rd floor Engineering Building",
        dateLostFound: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        type: "lost",
        reporterName: "Jennifer Wu",
        reporterEmail: "<EMAIL>",
        reporterPhone: "******-0126",
        images: ["https://images.unsplash.com/photo-1517336714731-489689fd1ca4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"],
        mapLocation: { lat: 40.7600, lng: -73.9830 }
      },
      {
        title: "Gold Necklace with Heart Pendant",
        description: "LOST: Delicate gold chain necklace with small heart pendant. Has sentimental value - it was a gift from my grandmother. Might have fallen off near the student center.",
        category: "accessories",
        location: "student-center",
        specificLocation: "Student Center, main lobby or study areas",
        dateLostFound: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
        type: "lost",
        reporterName: "Maria Rodriguez",
        reporterEmail: "<EMAIL>",
        reporterPhone: "******-0127",
        images: ["https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"],
        mapLocation: { lat: 40.7585, lng: -73.9855 }
      }
    ];

    for (const item of sampleItems) {
      try {
        await storage.createItem(item);
        console.log(`Created item: ${item.title}`);
      } catch (error) {
        console.error(`Error creating item ${item.title}:`, error);
      }
    }

    console.log("Database seeded successfully!");
  } catch (error) {
    console.error("Error seeding database:", error);
  }
}