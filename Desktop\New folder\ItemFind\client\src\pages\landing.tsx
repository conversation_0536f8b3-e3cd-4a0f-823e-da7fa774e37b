import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { SearchIcon, PlusIcon, ShieldCheckIcon, UserIcon } from "lucide-react";

export default function Landing() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <SearchIcon className="h-8 w-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">CampusFind</h1>
            </div>
            <Button onClick={() => window.location.href = '/api/login'} className="bg-blue-600 hover:bg-blue-700">
              Sign In
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Lost Something? Found Something?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            CampusFind is your trusted platform for reporting and finding lost items in campus and community environments. 
            Connect items with their rightful owners quickly and securely.
          </p>
          <div className="flex justify-center space-x-4">
            <Button 
              size="lg" 
              onClick={() => window.location.href = '/api/login'}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Get Started
            </Button>
            <Button size="lg" variant="outline">
              Learn More
            </Button>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">How It Works</h3>
          <p className="text-lg text-gray-600">Simple steps to reunite lost items with their owners</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card>
            <CardHeader className="text-center">
              <PlusIcon className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <CardTitle>Report Found Items</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-center">
                Found something? Report it with photos and detailed descriptions to help owners identify their belongings.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <SearchIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <CardTitle>Search Lost Items</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-center">
                Browse through reported items using smart filters. Search by category, location, date, and keywords.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <ShieldCheckIcon className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <CardTitle>Secure Claims</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-center">
                Verified claim process ensures items return to their rightful owners. Admin verification for security.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* User Types */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">Who Can Use CampusFind?</h3>
            <p className="text-lg text-gray-600">Designed for various community members</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <UserIcon className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-gray-900 mb-2">Students & Staff</h4>
              <p className="text-gray-600">
                Perfect for campus communities. Report and find items lost in classrooms, libraries, cafeterias, and more.
              </p>
            </div>

            <div className="text-center">
              <UserIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-gray-900 mb-2">Community Residents</h4>
              <p className="text-gray-600">
                Great for barangay and neighborhood communities. Help neighbors recover lost belongings.
              </p>
            </div>

            <div className="text-center">
              <ShieldCheckIcon className="h-16 w-16 text-purple-600 mx-auto mb-4" />
              <h4 className="text-xl font-semibold text-gray-900 mb-2">Security Officers</h4>
              <p className="text-gray-600">
                Admin tools for security personnel to verify claims and manage the platform effectively.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h3>
          <p className="text-xl text-blue-100 mb-8">
            Join our community and help reunite lost items with their owners.
          </p>
          <Button 
            size="lg" 
            variant="secondary"
            onClick={() => window.location.href = '/api/login'}
            className="bg-white text-blue-600 hover:bg-gray-100"
          >
            Sign In Now
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <SearchIcon className="h-6 w-6" />
            <span className="text-xl font-bold">CampusFind</span>
          </div>
          <p className="text-gray-400">
            Connecting lost items with their owners, one community at a time.
          </p>
        </div>
      </footer>
    </div>
  );
}