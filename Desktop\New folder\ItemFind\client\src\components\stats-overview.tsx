import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";

interface StatsData {
  totalItems: number;
  availableItems: number;
  claimedItems: number;
  returnedItems: number;
}

export default function StatsOverview() {
  const { data: stats, isLoading } = useQuery<StatsData>({
    queryKey: ["/api/stats"],
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6 text-center">
              <div className="h-8 bg-gray-200 rounded mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <Card>
        <CardContent className="p-6 text-center">
          <div className="text-3xl font-bold text-primary mb-2">
            {stats?.totalItems || 0}
          </div>
          <div className="text-gray-600">Total Items Found</div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6 text-center">
          <div className="text-3xl font-bold text-green-500 mb-2">
            {stats?.availableItems || 0}
          </div>
          <div className="text-gray-600">Available Items</div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6 text-center">
          <div className="text-3xl font-bold text-green-600 mb-2">
            {stats?.returnedItems || 0}
          </div>
          <div className="text-gray-600">Successful Returns</div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6 text-center">
          <div className="text-3xl font-bold text-orange-500 mb-2">
            {stats?.claimedItems || 0}
          </div>
          <div className="text-gray-600">Claimed Items</div>
        </CardContent>
      </Card>
    </div>
  );
}
