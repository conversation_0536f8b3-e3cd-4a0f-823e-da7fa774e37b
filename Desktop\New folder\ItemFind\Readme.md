# Replit.md

## Overview

This is a full-stack lost and found web application called **CampusFind** built with React frontend and Express backend. The application allows users to report found items and browse/search for lost items in a campus environment. It uses a modern tech stack with TypeScript, Tailwind CSS, shadcn/ui components, and Drizzle ORM with PostgreSQL for data persistence.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with Vite as the build tool
- **Routing**: Wouter for client-side routing (lightweight alternative to React Router)
- **State Management**: TanStack Query (React Query) for server state management
- **UI Framework**: shadcn/ui components built on top of Radix UI primitives
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **Form Handling**: React Hook Form with Zod validation
- **TypeScript**: Full TypeScript support with strict mode enabled

### Backend Architecture  
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Validation**: Zod schemas for request/response validation
- **Database Provider**: Neon Database (serverless PostgreSQL)
- **Development**: Hot reloading with Vite integration in development mode

## Key Components

### Database Schema (shared/schema.ts)
- **Items Table**: Stores found items with fields like title, description, category, location, finder info, and status
- **Claims Table**: Stores claims made by people trying to recover their items
- **Status Management**: Items can be "available", "claimed", or "returned"
- **UUID Primary Keys**: Auto-generated UUIDs for all records

### API Routes (server/routes.ts)
- `GET /api/items` - Fetch items with optional filtering (category, location, status, search)
- `GET /api/items/:id` - Get single item details
- `POST /api/items` - Create new found item report
- Claim-related endpoints for managing item recovery requests
- Statistics endpoint for dashboard metrics

### Storage Layer (server/storage.ts)
- **Interface-based Design**: IStorage interface allows for different storage implementations
- **Current Implementation**: In-memory storage with sample data for development
- **Future-ready**: Can be easily swapped for database-backed storage
- **Operations**: CRUD operations for items and claims, plus statistics aggregation

### Frontend Pages
- **Home Page**: Browse items with search/filter functionality, stats overview, recent activity sidebar
- **Report Item Page**: Form for reporting found items with image upload capability
- **Navigation**: Clean navbar with user-friendly navigation between sections

## Data Flow

1. **Item Reporting**: Users fill out form → validation with Zod → API call to create item → update in storage
2. **Item Browsing**: Query API with filters → receive filtered items → display in card layout
3. **Search/Filter**: Real-time filtering through query parameters → backend filtering → updated results
4. **Claims Process**: Users can claim items → creates claim record → changes item status

## External Dependencies

### UI and Styling
- **Radix UI**: Comprehensive set of accessible, unstyled UI primitives
- **Tailwind CSS**: Utility-first CSS framework with custom color scheme
- **Lucide React**: Icon library for consistent iconography
- **Class Variance Authority**: For creating type-safe, variant-based component APIs

### Data and Forms
- **TanStack Query**: Server state management with caching, background updates
- **React Hook Form**: Performant forms with minimal re-renders
- **Zod**: TypeScript-first schema validation for forms and API
- **date-fns**: Modern JavaScript date utility library

### Database and Backend
- **Drizzle ORM**: TypeScript ORM with excellent type safety
- **@neondatabase/serverless**: Serverless PostgreSQL database driver
- **Express**: Minimal web framework for Node.js

## Deployment Strategy

### Development
- **Vite Dev Server**: Hot module replacement for fast development
- **Express Development**: TSX for running TypeScript directly with auto-restart
- **Integrated Setup**: Vite middleware integrated with Express in development

### Production Build
- **Frontend**: Vite builds optimized React bundle to `dist/public`
- **Backend**: esbuild bundles Express server to `dist/index.js`
- **Static Serving**: Express serves built frontend files in production
- **Database**: Uses DATABASE_URL environment variable for PostgreSQL connection

### Environment Configuration
- **Development**: NODE_ENV=development with Vite dev server
- **Production**: NODE_ENV=production with built assets
- **Database**: Drizzle configured for PostgreSQL with migrations in `./migrations`
- **Replit Integration**: Special handling for Replit environment with development banner

The application is designed to be easily deployable on platforms like Replit, Vercel, or any Node.js hosting service with PostgreSQL database support.