import { Card, CardContent } from "@/components/ui/card";

export default function Sidebar() {
  const recentActivity = [
    { action: "<PERSON><PERSON><PERSON> returned to owner", time: "5 minutes ago", type: "success" },
    { action: "New item reported at Library", time: "12 minutes ago", type: "info" },
    { action: "Wallet claimed by owner", time: "1 hour ago", type: "warning" },
  ];

  const topLocations = [
    { name: "Main Library", count: 23 },
    { name: "Student Center", count: 18 },
    { name: "Gymnasium", count: 15 },
    { name: "Cafeteria", count: 12 },
  ];

  return (
    <div className="lg:w-1/4 space-y-6">
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === "success" ? "bg-green-600" :
                  activity.type === "info" ? "bg-primary" : "bg-orange-500"
                }`}></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-700">{activity.action}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Locations</h3>
          <div className="space-y-3">
            {topLocations.map((location, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-gray-700">{location.name}</span>
                <span className="text-sm font-medium text-primary">{location.count} items</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
