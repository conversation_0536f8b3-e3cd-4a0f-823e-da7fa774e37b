import { Link, useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { SearchIcon, PlusIcon, BellIcon, UserIcon } from "lucide-react";

export default function Navbar() {
  const [location] = useLocation();

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2">
              <SearchIcon className="text-primary text-2xl" />
              <h1 className="text-xl font-bold text-gray-900">CampusFind</h1>
            </Link>
            <nav className="hidden md:flex space-x-6">
              <Link href="/" className={location === "/" ? "text-primary font-medium" : "text-gray-600 hover:text-primary"}>
                Browse Items
              </Link>
              <Link href="/report" className={location === "/report" ? "text-primary font-medium" : "text-gray-600 hover:text-primary"}>
                Report Found Item
              </Link>
              <a href="#" className="text-gray-600 hover:text-primary">My Reports</a>
            </nav>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/report">
              <Button className="bg-green-600 hover:bg-green-700 text-white">
                <PlusIcon className="w-4 h-4 mr-2" />
                Report Found Item
              </Button>
            </Link>
            <Button variant="ghost" size="icon" className="text-gray-600 hover:text-primary">
              <BellIcon className="w-5 h-5" />
            </Button>
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <UserIcon className="w-4 h-4 text-white" />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
