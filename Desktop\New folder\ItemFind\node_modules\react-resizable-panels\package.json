{"name": "react-resizable-panels", "version": "2.1.7", "description": "React components for resizable panel groups/layouts", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/bvaughn/react-resizable-panels.git"}, "source": "src/index.ts", "main": "dist/react-resizable-panels.cjs.js", "module": "dist/react-resizable-panels.esm.js", "files": ["dist", "package.json", "README.md", "LICENSE"], "exports": {".": {"types": {"import": "./dist/react-resizable-panels.cjs.mjs", "default": "./dist/react-resizable-panels.cjs.js"}, "development": {"browser": {"module": "./dist/react-resizable-panels.browser.development.esm.js", "import": "./dist/react-resizable-panels.browser.development.cjs.mjs", "default": "./dist/react-resizable-panels.browser.development.cjs.js"}, "node": {"module": "./dist/react-resizable-panels.development.node.esm.js", "import": "./dist/react-resizable-panels.development.node.cjs.mjs", "default": "./dist/react-resizable-panels.development.node.cjs.js"}, "module": "./dist/react-resizable-panels.development.esm.js", "import": "./dist/react-resizable-panels.development.cjs.mjs", "default": "./dist/react-resizable-panels.development.cjs.js"}, "browser": {"module": "./dist/react-resizable-panels.browser.esm.js", "import": "./dist/react-resizable-panels.browser.cjs.mjs", "default": "./dist/react-resizable-panels.browser.cjs.js"}, "node": {"module": "./dist/react-resizable-panels.node.esm.js", "import": "./dist/react-resizable-panels.node.cjs.mjs", "default": "./dist/react-resizable-panels.node.cjs.js"}, "module": "./dist/react-resizable-panels.esm.js", "import": "./dist/react-resizable-panels.cjs.mjs", "default": "./dist/react-resizable-panels.cjs.js"}, "./package.json": "./package.json"}, "imports": {"#is-development": {"development": "./src/env-conditions/development.ts", "default": "./src/env-conditions/production.ts"}, "#is-browser": {"browser": "./src/env-conditions/browser.ts", "node": "./src/env-conditions/node.ts", "default": "./src/env-conditions/unknown.ts"}}, "types": "dist/react-resizable-panels.cjs.d.ts", "scripts": {"clear": "pnpm run clear:builds & pnpm run clear:node_modules", "clear:builds": "rm -rf ./packages/*/dist", "clear:node_modules": "rm -rf ./node_modules", "lint": "eslint \"src/**/*.{ts,tsx}\"", "test": "jest --config=jest.config.js", "test:watch": "jest --config=jest.config.js --watch", "watch": "parcel watch --port=2345"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "eslint": "^8.37.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "react": "experimental", "react-dom": "experimental"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "browserslist": ["Chrome 79"]}