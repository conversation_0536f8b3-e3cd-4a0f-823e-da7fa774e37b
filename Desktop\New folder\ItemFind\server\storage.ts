import { type Item, type InsertItem, type Claim, type InsertClaim, type User, type UpsertUser, type Notification, type InsertNotification } from "@shared/schema";
import { users, items, claims, notifications } from "@shared/schema";
import { db } from "./db";
import { eq, and, desc, ilike, or, sql } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  upsertUser(user: UpsertUser): Promise<User>;
  updateUser(id: string, updates: Partial<User>): Promise<User | undefined>;
  
  // Item operations
  getItems(filters?: {
    category?: string;
    location?: string;
    status?: string;
    search?: string;
    type?: string;
    userId?: string;
  }): Promise<Item[]>;
  getItem(id: string): Promise<Item | undefined>;
  createItem(item: InsertItem): Promise<Item>;
  updateItem(id: string, updates: Partial<Item>): Promise<Item | undefined>;
  deleteItem(id: string): Promise<boolean>;
  
  // Claim operations
  getClaims(filters?: {
    itemId?: string;
    userId?: string;
    status?: string;
  }): Promise<Claim[]>;
  getClaim(id: string): Promise<Claim | undefined>;
  createClaim(claim: InsertClaim): Promise<Claim>;
  updateClaim(id: string, updates: Partial<Claim>): Promise<Claim | undefined>;
  
  // Notification operations
  getNotifications(userId: string): Promise<Notification[]>;
  createNotification(notification: InsertNotification): Promise<Notification>;
  markNotificationRead(id: string): Promise<boolean>;
  
  // Statistics
  getStats(): Promise<{
    totalItems: number;
    availableItems: number;
    claimedItems: number;
    returnedItems: number;
    totalUsers: number;
    pendingClaims: number;
  }>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(userData)
      .onConflictDoUpdate({
        target: users.id,
        set: {
          ...userData,
          updatedAt: new Date(),
        },
      })
      .returning();
    return user;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | undefined> {
    const [user] = await db
      .update(users)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return user || undefined;
  }

  // Item operations
  async getItems(filters?: {
    category?: string;
    location?: string;
    status?: string;
    search?: string;
    type?: string;
    userId?: string;
  }): Promise<Item[]> {
    let query = db.select().from(items);
    const conditions = [];

    if (filters) {
      if (filters.category) {
        conditions.push(eq(items.category, filters.category));
      }
      if (filters.location) {
        conditions.push(eq(items.location, filters.location));
      }
      if (filters.status) {
        conditions.push(eq(items.status, filters.status));
      }
      if (filters.type) {
        conditions.push(eq(items.type, filters.type));
      }
      if (filters.userId) {
        conditions.push(eq(items.userId, filters.userId));
      }
      if (filters.search) {
        conditions.push(
          or(
            ilike(items.title, `%${filters.search}%`),
            ilike(items.description, `%${filters.search}%`)
          )
        );
      }
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const result = await query.orderBy(desc(items.createdAt));
    return result;
  }

  async getItem(id: string): Promise<Item | undefined> {
    const [item] = await db.select().from(items).where(eq(items.id, id));
    return item || undefined;
  }

  async createItem(insertItem: InsertItem): Promise<Item> {
    const [item] = await db
      .insert(items)
      .values(insertItem)
      .returning();
    return item;
  }

  async updateItem(id: string, updates: Partial<Item>): Promise<Item | undefined> {
    const [item] = await db
      .update(items)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(items.id, id))
      .returning();
    return item || undefined;
  }

  async deleteItem(id: string): Promise<boolean> {
    const result = await db.delete(items).where(eq(items.id, id));
    return result.rowCount > 0;
  }

  // Claim operations
  async getClaims(filters?: {
    itemId?: string;
    userId?: string;
    status?: string;
  }): Promise<Claim[]> {
    let query = db.select().from(claims);
    const conditions = [];

    if (filters) {
      if (filters.itemId) {
        conditions.push(eq(claims.itemId, filters.itemId));
      }
      if (filters.userId) {
        conditions.push(eq(claims.userId, filters.userId));
      }
      if (filters.status) {
        conditions.push(eq(claims.status, filters.status));
      }
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const result = await query.orderBy(desc(claims.createdAt));
    return result;
  }

  async getClaim(id: string): Promise<Claim | undefined> {
    const [claim] = await db.select().from(claims).where(eq(claims.id, id));
    return claim || undefined;
  }

  async createClaim(insertClaim: InsertClaim): Promise<Claim> {
    const [claim] = await db
      .insert(claims)
      .values(insertClaim)
      .returning();
    return claim;
  }

  async updateClaim(id: string, updates: Partial<Claim>): Promise<Claim | undefined> {
    const [claim] = await db
      .update(claims)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(claims.id, id))
      .returning();
    return claim || undefined;
  }

  // Notification operations
  async getNotifications(userId: string): Promise<Notification[]> {
    const result = await db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, userId))
      .orderBy(desc(notifications.createdAt));
    return result;
  }

  async createNotification(insertNotification: InsertNotification): Promise<Notification> {
    const [notification] = await db
      .insert(notifications)
      .values(insertNotification)
      .returning();
    return notification;
  }

  async markNotificationRead(id: string): Promise<boolean> {
    const result = await db
      .update(notifications)
      .set({ isRead: true })
      .where(eq(notifications.id, id));
    return result.rowCount > 0;
  }

  // Statistics
  async getStats(): Promise<{
    totalItems: number;
    availableItems: number;
    claimedItems: number;
    returnedItems: number;
    totalUsers: number;
    pendingClaims: number;
  }> {
    const [itemStats] = await db
      .select({
        totalItems: sql<number>`count(*)`,
        availableItems: sql<number>`count(*) filter (where status = 'available')`,
        claimedItems: sql<number>`count(*) filter (where status = 'claimed')`,
        returnedItems: sql<number>`count(*) filter (where status = 'returned')`,
      })
      .from(items);

    const [userStats] = await db
      .select({
        totalUsers: sql<number>`count(*)`,
      })
      .from(users);

    const [claimStats] = await db
      .select({
        pendingClaims: sql<number>`count(*) filter (where status = 'pending')`,
      })
      .from(claims);

    return {
      totalItems: itemStats.totalItems || 0,
      availableItems: itemStats.availableItems || 0,
      claimedItems: itemStats.claimedItems || 0,
      returnedItems: itemStats.returnedItems || 0,
      totalUsers: userStats.totalUsers || 0,
      pendingClaims: claimStats.pendingClaims || 0,
    };
  }
}

export const storage = new DatabaseStorage();
