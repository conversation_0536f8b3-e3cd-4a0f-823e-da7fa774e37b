"use strict";
exports.arEG = void 0;
var _index = require("./ar-EG/_lib/formatDistance.js");
var _index2 = require("./ar-EG/_lib/formatLong.js");
var _index3 = require("./ar-EG/_lib/formatRelative.js");
var _index4 = require("./ar-EG/_lib/localize.js");
var _index5 = require("./ar-EG/_lib/match.js");

/**
 * @category Locales
 * @summary Arabic locale (Egypt).
 * @language Arabic
 * @iso-639-2 ara
 * <AUTHOR> [@AbdAllahAbdElFattah13](https://github.com/AbdAllahAbdElFattah13)
 */
const arEG = (exports.arEG = {
  code: "ar-EG",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
