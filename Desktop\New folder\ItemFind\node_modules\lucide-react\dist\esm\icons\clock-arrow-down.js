/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const ClockArrowDown = createLucideIcon("ClockArrowDown", [
  ["path", { d: "M12.338 21.994A10 10 0 1 1 21.925 13.227", key: "1i7shu" }],
  ["path", { d: "M12 6v6l2 1", key: "19cm8n" }],
  ["path", { d: "m14 18 4 4 4-4", key: "1waygx" }],
  ["path", { d: "M18 14v8", key: "irew45" }]
]);

export { ClockArrowDown as default };
//# sourceMappingURL=clock-arrow-down.js.map
