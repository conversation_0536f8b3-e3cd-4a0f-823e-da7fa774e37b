@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(140, 50%, 45%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(28, 100%, 50%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  --available: hsl(122, 39%, 49%);
  --claimed: hsl(28, 100%, 50%);
  --returned: hsl(0, 0%, 62%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(140, 50%, 45%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(28, 100%, 50%);
  --accent-foreground: hsl(0, 0%, 100%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  --available: hsl(122, 39%, 49%);
  --claimed: hsl(28, 100%, 50%);
  --returned: hsl(0, 0%, 62%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .status-available {
    @apply bg-green-500 text-white;
  }
  .status-claimed {
    @apply bg-orange-500 text-white;
  }
  .status-returned {
    @apply bg-gray-500 text-white;
  }
  .shadow-card {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
  .shadow-card-hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  }
}
