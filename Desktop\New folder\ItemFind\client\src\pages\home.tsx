import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { SearchIcon, HandIcon } from "lucide-react";
import SearchFilters from "@/components/search-filters";
import StatsOverview from "@/components/stats-overview";
import ItemCard from "@/components/item-card";
import Sidebar from "@/components/sidebar";
import type { Item } from "@shared/schema";

export default function Home() {
  const [searchQuery, setSearchQuery] = useState("");
  const [category, setCategory] = useState("all");
  const [location, setLocation] = useState("all");
  const [status, setStatus] = useState("all");

  const { data: items, isLoading } = useQuery<Item[]>({
    queryKey: ["/api/items", searchQuery, category === "all" ? "" : category, location === "all" ? "" : location, status === "all" ? "" : status],
  });

  const handleViewDetails = (item: Item) => {
    // TODO: Implement item detail modal or page
    console.log("View details for item:", item);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary to-blue-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold mb-4">Lost Something? We'll Help You Find It</h2>
              <p className="text-xl mb-8 text-blue-100">
                Connect with your community to recover lost items. Report found items and help others reunite with their belongings.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-white text-primary hover:bg-gray-50">
                  <SearchIcon className="w-5 h-5 mr-2" />
                  Search Lost Items
                </Button>
                <Link href="/report">
                  <Button size="lg" variant="outline" className="border-2 border-white text-white hover:bg-white hover:text-primary">
                    <HandIcon className="w-5 h-5 mr-2" />
                    Report Found Item
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
                alt="Students helping each other on campus"
                className="rounded-xl shadow-2xl w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <SearchFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          category={category}
          onCategoryChange={setCategory}
          location={location}
          onLocationChange={setLocation}
          status={status}
          onStatusChange={setStatus}
        />

        <StatsOverview />

        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-3/4">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Found Items</h3>
            </div>

            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-white rounded-xl shadow-card p-6 animate-pulse">
                    <div className="h-48 bg-gray-200 rounded mb-4"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded mb-4 w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : items && items.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {items.map((item: Item) => (
                  <ItemCard
                    key={item.id}
                    item={item}
                    onViewDetails={handleViewDetails}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <SearchIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
                <p className="text-gray-600">Try adjusting your search filters or check back later.</p>
              </div>
            )}
          </div>

          <Sidebar />
        </div>
      </main>
    </div>
  );
}
