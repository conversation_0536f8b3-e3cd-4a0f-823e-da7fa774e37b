import session from "express-session";
import type { Express, RequestHandler } from "express";
import { storage } from "./storage";
import { db } from "./db";
import { sessions } from "@shared/schema";
import { eq } from "drizzle-orm";

// Simple session store using SQLite
class SQLiteStore extends session.Store {
  constructor() {
    super();
  }

  get(sid: string, callback: (err?: any, session?: session.SessionData | null) => void) {
    try {
      const result = db.select().from(sessions).where(eq(sessions.sid, sid)).all();
      if (!result || result.length === 0) {
        return callback(null, null);
      }

      const sessionRecord = result[0];
      // Check if session has expired
      if (sessionRecord.expire && new Date(sessionRecord.expire) < new Date()) {
        this.destroy(sid, () => {});
        return callback(null, null);
      }

      const sessionData = JSON.parse(sessionRecord.sess);
      callback(null, sessionData);
    } catch (error) {
      callback(error);
    }
  }

  set(sid: string, session: session.SessionData, callback?: (err?: any) => void) {
    try {
      const expire = session.cookie?.expires || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      const sess = JSON.stringify(session);

      // First try to update, if no rows affected then insert
      const updateResult = db.update(sessions)
        .set({ sess, expire })
        .where(eq(sessions.sid, sid))
        .run();

      if (updateResult.changes === 0) {
        db.insert(sessions)
          .values({ sid, sess, expire })
          .run();
      }

      callback?.();
    } catch (error) {
      callback?.(error);
    }
  }

  destroy(sid: string, callback?: (err?: any) => void) {
    try {
      db.delete(sessions).where(eq(sessions.sid, sid)).run();
      callback?.();
    } catch (error) {
      callback?.(error);
    }
  }
}

export function getSession() {
  const sessionTtl = 7 * 24 * 60 * 60 * 1000; // 1 week
  const sessionStore = new SQLiteStore();
  
  return session({
    secret: process.env.SESSION_SECRET!,
    store: sessionStore,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: sessionTtl,
    },
  });
}

export async function setupAuth(app: Express) {
  app.set("trust proxy", 1);
  app.use(getSession());

  // Simple login endpoint for development/testing
  app.post("/api/login", async (req, res) => {
    const { email, name } = req.body;
    
    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    try {
      // Create or get user
      let user = await storage.getUserByEmail(email);
      if (!user) {
        user = await storage.createUser({
          email,
          firstName: name?.split(' ')[0] || 'User',
          lastName: name?.split(' ').slice(1).join(' ') || '',
          role: 'user',
          isGuest: false
        });
      }

      // Set session
      (req.session as any).user = {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      };

      res.json({ user });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  // Guest login endpoint
  app.post("/api/login/guest", async (req, res) => {
    try {
      const guestUser = await storage.createUser({
        email: `guest_${Date.now()}@example.com`,
        firstName: 'Guest',
        lastName: 'User',
        role: 'user',
        isGuest: true
      });

      (req.session as any).user = {
        id: guestUser.id,
        email: guestUser.email,
        firstName: guestUser.firstName,
        lastName: guestUser.lastName,
        role: guestUser.role,
        isGuest: true
      };

      res.json({ user: guestUser });
    } catch (error) {
      console.error("Guest login error:", error);
      res.status(500).json({ message: "Guest login failed" });
    }
  });

  // Logout endpoint
  app.post("/api/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout failed" });
      }
      res.json({ message: "Logged out successfully" });
    });
  });
}

export const isAuthenticated: RequestHandler = async (req, res, next) => {
  const sessionUser = (req.session as any)?.user;
  
  if (!sessionUser) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  // Attach user to request
  (req as any).user = sessionUser;
  next();
};

export const isAdminOrSecurity: RequestHandler = async (req, res, next) => {
  const sessionUser = (req.session as any)?.user;
  
  if (!sessionUser) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  if (sessionUser.role !== "admin" && sessionUser.role !== "security") {
    return res.status(403).json({ message: "Access denied. Admin or security role required." });
  }

  next();
};
