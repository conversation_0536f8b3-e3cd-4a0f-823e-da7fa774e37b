import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPinIcon, CalendarIcon, UserIcon } from "lucide-react";
import { type Item } from "@shared/schema";

interface ItemCardProps {
  item: Item;
  onViewDetails: (item: Item) => void;
}

export default function ItemCard({ item, onViewDetails }: ItemCardProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "available":
        return <Badge className="status-available">Available</Badge>;
      case "claimed":
        return <Badge className="status-claimed">Claimed</Badge>;
      case "returned":
        return <Badge className="status-returned">Returned</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateValue: string | Date) => {
    const date = new Date(dateValue);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return "1 day ago";
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 14) return "1 week ago";
    return `${Math.floor(diffDays / 7)} weeks ago`;
  };

  const formatLocation = (location: string) => {
    const locationMap: { [key: string]: string } = {
      library: "Main Library",
      cafeteria: "Cafeteria",
      gym: "Gymnasium",
      parking: "Parking Lot",
      dorms: "Dormitories",
      classroom: "Classrooms",
    };
    return locationMap[location] || location;
  };

  return (
    <Card className="hover:shadow-card-hover transition-shadow cursor-pointer">
      <div className="relative">
        <img
          src={item.images?.[0] || "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"}
          alt={item.title}
          className="w-full h-48 object-cover rounded-t-xl"
        />
        <div className="absolute top-3 right-3">
          {getStatusBadge(item.status)}
        </div>
      </div>
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-3">
          <h4 className="text-lg font-semibold text-gray-900">{item.title}</h4>
          <span className="text-sm text-gray-500 capitalize">{item.category}</span>
        </div>
        <p className="text-gray-600 mb-4 line-clamp-2">{item.description}</p>
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <MapPinIcon className="w-4 h-4 mr-1" />
            <span>{formatLocation(item.location)}</span>
          </div>
          <div className="flex items-center">
            <CalendarIcon className="w-4 h-4 mr-1" />
            <span>{formatDate(item.dateLostFound)}</span>
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="flex items-center text-sm text-gray-600">
            <UserIcon className="w-4 h-4 mr-1" />
            <span>{item.reporterName}</span>
          </div>
          <Button
            onClick={() => onViewDetails(item)}
            disabled={item.status === "returned"}
            className={item.status === "returned" ? "bg-gray-400 cursor-not-allowed" : ""}
          >
            {item.status === "returned" ? "Returned" : 
             item.status === "claimed" ? "Claimed" : "View Details"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
